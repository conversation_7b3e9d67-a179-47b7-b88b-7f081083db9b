'use client'

import React from 'react'

interface SetCellProps {
  isHeaderCell?: boolean
  setNo?: number | string
  reps?: number
  weight?: number
  isFinished?: boolean
  isBodyweight?: boolean
  isLastSet?: boolean
  isExerciseFinished?: boolean
  isNext?: boolean
  backColor?: string
  weightSingal?: string
  setData?: {
    IsBackOffSet?: boolean
    IsWarmups?: boolean
    IsFinished?: boolean
    IsEditing?: boolean
    IsFirstWorkSet?: boolean
  } // WorkoutLogSerieModelRef data for MAUI-style handlers
  exercise?: {
    Id?: number
    Name?: string
    IsBodyweight?: boolean
  } // ExerciseModel for 1RM calculations
  recommendation?: {
    ExerciseId?: number
    Weight?: { Lb: number; Kg: number }
    Reps?: number
    Series?: number
  } // RecommendationModel for 1RM calculations
  userBodyWeight?: number
  onRepsChange?: (reps: number) => void
  onWeightChange?: (weight: number) => void
  onSetComplete?: () => void
  onFinishExercise?: () => void
  onAddSet?: () => void
  onOneRMUpdate?: (data: {
    weight: number
    reps: number
    exercise?: {
      Id?: number
      Name?: string
      IsBodyweight?: boolean
    }
    recommendation?: {
      ExerciseId?: number
      Weight?: { Lb: number; Kg: number }
      Reps?: number
      Series?: number
    }
    isKg: boolean
    userBodyWeight: number
    isFirstWorkSet?: boolean
  }) => void
  unit?: 'kg' | 'lbs'
}

export function SetCell({
  isHeaderCell = false,
  setNo = 1,
  reps = 0,
  weight = 0,
  isFinished = false,
  isBodyweight = false,
  isLastSet = false,
  isExerciseFinished = false,
  isNext = false,
  backColor = 'transparent',
  weightSingal,
  setData,
  exercise,
  recommendation,
  userBodyWeight = 80,
  onRepsChange,
  onWeightChange,
  onSetComplete,
  onFinishExercise,
  onAddSet,
  onOneRMUpdate,
  unit = 'lbs',
}: SetCellProps) {
  const handleRepsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target

    // Use MAUI-style validation
    if (value.endsWith(',') || value.endsWith('.') || value === '') return

    const numValue = parseInt(value.replace(',', '').replace('.', ''), 10)
    if (!Number.isNaN(numValue)) {
      onRepsChange?.(numValue)

      // Trigger 1RM update for first work sets (matching MAUI logic)
      if (
        setData &&
        !setData.IsBackOffSet &&
        !setData.IsWarmups &&
        !setData.IsFinished &&
        !setData.IsEditing
      ) {
        // This would trigger 1RM recalculation in a real implementation
        onOneRMUpdate?.({
          weight,
          reps: numValue,
          exercise,
          recommendation,
          isKg: unit === 'kg',
          userBodyWeight,
          isFirstWorkSet: setData.IsFirstWorkSet,
        })
      }
    }
  }

  const handleWeightChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target

    if (value === '') return

    // Replace comma with dot and remove spaces (MAUI style)
    const entryText = value.replace(',', '.').replace(' ', '')
    const numValue = parseFloat(entryText)

    if (!Number.isNaN(numValue) && !entryText.endsWith('.')) {
      onWeightChange?.(numValue)

      // Trigger 1RM update for first work sets (matching MAUI logic)
      if (
        setData &&
        !setData.IsBackOffSet &&
        !setData.IsWarmups &&
        !setData.IsFinished &&
        !setData.IsEditing
      ) {
        // This would trigger 1RM recalculation in a real implementation
        onOneRMUpdate?.({
          weight: numValue,
          reps,
          exercise,
          recommendation,
          isKg: unit === 'kg',
          userBodyWeight,
          isFirstWorkSet: setData.IsFirstWorkSet,
        })
      }
    }
  }

  // Format weight display (matching MAUI WeightSingal logic)
  const getFormattedWeight = () => {
    if (weightSingal) return weightSingal
    if (isBodyweight) return '0'
    // Return raw weight value without formatting
    return weight
  }

  /**
   * Get dynamic background color based on set state
   * Matches MAUI BackColor property logic but uses web app theme colors
   */
  const getBackgroundColor = () => {
    if (backColor && backColor !== 'transparent') {
      return backColor
    }

    // Web app theme-aware background colors
    if (isNext) {
      return 'bg-brand-primary/10' // Highlight next set
    }

    if (isFinished) {
      return 'bg-success/5' // Subtle success background for completed sets
    }

    return '' // Default transparent
  }

  /**
   * Get dynamic styling classes based on set state
   */
  const getSetStateClasses = () => {
    const baseClasses = 'px-4'
    const backgroundClass = getBackgroundColor()

    let stateClasses = ''

    if (isNext) {
      stateClasses += ' ring-2 ring-brand-primary'
    }

    if (isFinished) {
      stateClasses += ' opacity-75'
    }

    return `${baseClasses} ${backgroundClass} ${stateClasses}`.trim()
  }

  if (isHeaderCell) {
    return (
      <div className="grid grid-cols-[25px_60px_1fr_25px_1fr] gap-0 px-4 py-3 bg-brand-primary text-text-inverse font-bold">
        <div />
        <div className="text-center">SET</div>
        <div className="text-center">REPS</div>
        <div className="text-center">*</div>
        <div className="text-center">{unit.toUpperCase()}</div>
      </div>
    )
  }

  // Handle arrow clicks for active sets
  const handleRepsArrowClick = (direction: 'up' | 'down') => {
    if (!isNext || isFinished) return

    if (direction === 'down' && (reps || 0) <= 1) {
      // Don't allow reps to go below 1
      return
    }

    const newReps = direction === 'up' ? (reps || 0) + 1 : (reps || 0) - 1
    onRepsChange?.(newReps)
  }

  const handleWeightArrowClick = (direction: 'up' | 'down') => {
    if (!isNext || isFinished || isBodyweight) return

    const increment = unit === 'kg' ? 2.5 : 5

    if (direction === 'down' && (weight || 0) - increment < 0) {
      // Don't allow weight to go negative
      return
    }

    const newWeight =
      direction === 'up' ? (weight || 0) + increment : (weight || 0) - increment
    onWeightChange?.(newWeight)
  }

  return (
    <div className={getSetStateClasses()}>
      {/* Arrow buttons above inputs for active set */}
      {isNext && !isFinished && (
        <div className="grid grid-cols-[25px_60px_1fr_25px_1fr] gap-0 items-center pb-1">
          <div />
          <div />
          <div className="px-2 flex justify-center">
            <button
              onClick={() => handleRepsArrowClick('up')}
              className="text-brand-primary hover:text-brand-primary/80 transition-colors min-h-[44px] min-w-[44px] flex items-center justify-center"
              aria-label="Increase reps"
            >
              <svg
                className="w-12 h-12"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 15l7-7 7 7"
                />
              </svg>
            </button>
          </div>
          <div />
          <div className="px-2 flex justify-center">
            <button
              onClick={() => handleWeightArrowClick('up')}
              className="text-brand-primary hover:text-brand-primary/80 transition-colors min-h-[44px] min-w-[44px] flex items-center justify-center"
              aria-label="Increase weight"
            >
              <svg
                className="w-12 h-12"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 15l7-7 7 7"
                />
              </svg>
            </button>
          </div>
        </div>
      )}

      {/* Main Set Row */}
      <div className="grid grid-cols-[25px_60px_1fr_25px_1fr] gap-0 items-center py-2">
        {/* Check/Delete Icon */}
        <div className="flex items-center justify-center">
          {isFinished ? (
            <svg
              className="w-5 h-5 text-success cursor-pointer"
              data-testid="check-icon"
              fill="currentColor"
              viewBox="0 0 20 20"
              onClick={onSetComplete}
            >
              <path
                fillRule="evenodd"
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                clipRule="evenodd"
              />
            </svg>
          ) : (
            <svg
              className="w-5 h-5 text-error cursor-pointer hover:text-error/80"
              data-testid="delete-icon"
              fill="currentColor"
              viewBox="0 0 20 20"
              onClick={onSetComplete}
            >
              <path
                fillRule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          )}
        </div>

        {/* Set Number */}
        <div className="text-center text-lg font-medium text-text-secondary">
          {setNo}
        </div>

        {/* Reps Input */}
        <div className="px-2">
          <input
            type="number"
            value={reps || ''}
            onChange={handleRepsChange}
            disabled={isFinished}
            className="w-full text-center bg-bg-secondary border border-border-primary rounded-theme px-2 py-1 text-lg font-medium disabled:bg-bg-tertiary disabled:text-text-tertiary min-h-[44px]"
            aria-label={`Reps for set ${setNo}`}
            maxLength={4}
          />
        </div>

        {/* Multiplication Symbol */}
        <div className="text-center text-lg text-text-secondary">*</div>

        {/* Weight Input */}
        <div className="px-2">
          <input
            type="number"
            value={getFormattedWeight()}
            onChange={handleWeightChange}
            disabled={isFinished || isBodyweight}
            className="w-full text-center bg-bg-secondary border border-border-primary rounded-theme px-2 py-1 text-lg font-medium disabled:bg-bg-tertiary disabled:text-text-tertiary min-h-[44px]"
            aria-label={`Weight for set ${setNo}`}
          />
        </div>
      </div>

      {/* Arrow buttons below inputs for active set */}
      {isNext && !isFinished && (
        <div className="grid grid-cols-[25px_60px_1fr_25px_1fr] gap-0 items-center pt-1">
          <div />
          <div />
          <div className="px-2 flex justify-center">
            <button
              onClick={() => handleRepsArrowClick('down')}
              className="text-brand-primary hover:text-brand-primary/80 transition-colors min-h-[44px] min-w-[44px] flex items-center justify-center"
              aria-label="Decrease reps"
            >
              <svg
                className="w-12 h-12"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </button>
          </div>
          <div />
          <div className="px-2 flex justify-center">
            <button
              onClick={() => handleWeightArrowClick('down')}
              className="text-brand-primary hover:text-brand-primary/80 transition-colors min-h-[44px] min-w-[44px] flex items-center justify-center"
              aria-label="Decrease weight"
            >
              <svg
                className="w-12 h-12"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </button>
          </div>
        </div>
      )}

      {/* All sets done message - MultiTrigger: IsLastSet=True AND IsFinished=True AND IsExerciseFinished=False */}
      {isLastSet && isFinished && !isExerciseFinished && (
        <div className="mt-4 bg-success/10 rounded-theme p-4">
          <p className="text-center text-success font-medium italic">
            All sets done—congrats!
          </p>
        </div>
      )}

      {/* Finish Exercise Button - MultiTrigger: IsLastSet=True AND IsFinished=True */}
      {isLastSet && isFinished && onFinishExercise && (
        <div className="mt-4">
          <button
            onClick={onFinishExercise}
            className="w-full bg-success text-text-inverse font-bold py-4 rounded-theme text-lg hover:bg-success/90 transition-colors min-h-[66px]"
            aria-label="Finish this exercise and move to next"
          >
            Finish exercise
          </button>
        </div>
      )}

      {/* Add Set / Skip Exercise buttons - MultiTrigger: IsLastSet=True AND IsExerciseFinished=False */}
      {isLastSet && !isExerciseFinished && onAddSet && (
        <div className="mt-4">
          <button
            onClick={onAddSet}
            className="w-full border-2 border-accent text-text-primary bg-transparent font-medium py-4 rounded-theme hover:bg-accent/10 transition-colors min-h-[60px]"
          >
            Add set
          </button>
        </div>
      )}
    </div>
  )
}
