import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { renderHook, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useWorkout } from '../useWorkout'
import { useWorkoutStore } from '@/stores/workoutStore'
import { useAuthStore } from '@/stores/authStore'
import { workoutApi } from '@/api/workouts'
import * as workoutApiService from '@/services/api/workout'
import type {
  GetUserProgramInfoResponseModel,
  WorkoutTemplateModel,
  WorkoutTemplateGroupModel,
  RecommendationModel,
} from '@/types'

// Mock the API module
vi.mock('@/api/workouts', () => ({
  workoutApi: {
    getUserProgramInfo: vi.fn(),
    getUserWorkout: vi.fn(),
    getExerciseRecommendation: vi.fn(),
    saveWorkoutSet: vi.fn(),
    completeWorkout: vi.fn(),
  },
}))

// Mock the services/api/workout module which is actually used by useWorkoutDataLoader
vi.mock('@/services/api/workout', () => ({
  getUserWorkoutProgramInfo: vi.fn(),
  getUserWorkout: vi.fn(),
  getTodaysWorkout: vi.fn(),
}))

// Mock data
const mockUserProgramInfo: GetUserProgramInfoResponseModel = {
  GetUserProgramInfoResponseModel: {
    UserId: 'test-user',
    WeeklyStatus: 'Week 1',
    ProgramLabel: 'Test Program',
    NbDaysInTheWeek: 5,
    NbNonTrainingDays: 2,
    MondayIsFirst: false,
    TimeLogged: '2024-01-01T10:00:00',
    NextWorkoutDayText: 'Today',
    IsInIntroWorkout: false,
    IsInFirstWeek: true,
    TodaysWorkoutId: '1234',
    TodaysWorkoutText: 'Push Day',
    RecommendedProgram: {
      Id: 1,
      Label: 'Beginner Program',
      RemainingToLevelUp: 10,
      IconUrl: 'https://example.com/icon.png',
    },
    NextWorkoutTemplate: {
      Id: 1,
      Label: 'Push Day',
      IsSystemExercise: false,
      Exercises: [
        {
          Id: 1,
          Label: 'Bench Press',
          Path: 'chest/benchpress',
          TargetWeight: { Mass: 100, MassUnit: 'lbs' },
          TargetReps: 8,
          IsWarmup: false,
          HasPastLogs: true,
        },
      ],
    },
    NextNonTrainingDay: '2024-01-03',
    MondayHere: '2024-01-01',
    NextIntensityTechnique: 'Standard',
    ServerTimeUtc: '2024-01-01T10:00:00Z',
    MaxWorkoutSets: 20,
    NbMediumSets: 5,
    NbChallenges: 3,
    WorkoutTemplates: [],
  },
}

const mockWorkout: WorkoutTemplateModel = {
  Id: 1,
  UserId: 'test-user',
  Label: 'Push Day',
  Exercises: [
    {
      Id: 1,
      Label: 'Bench Press',
      Path: 'chest/benchpress',
      TargetWeight: { Mass: 100, MassUnit: 'lbs' },
      TargetReps: 8,
      IsWarmup: false,
      HasPastLogs: true,
    },
  ],
  IsSystemExercise: false,
  WorkoutSettingsModel: {
    Id: 1,
    Pause: 120,
    Equipment: '',
    ChildWorkoutTemplateId: null,
    SetsModel: null,
    WorkoutProgramId: 1,
    IsFirstSet: false,
    IsFail: false,
    NbRepsMinimalInc: null,
    AvgDuration: null,
    IsNotRealData: false,
  },
}

const mockWorkoutGroup: WorkoutTemplateGroupModel = {
  Id: 1,
  Label: 'Beginner Program',
  WorkoutTemplates: [mockWorkout],
  IsFeaturedProgram: false,
  UserId: '',
  IsSystemExercise: true,
  RequiredWorkoutToLevelUp: 10,
  ProgramId: 1,
}

const mockRecommendation: RecommendationModel = {
  Series: 3,
  Reps: 10,
  Weight: { Lb: 100, Kg: 45.36 },
  Increments: { Lb: 5, Kg: 2.5 },
  LastLogDate: '2024-01-01',
  LastReps: 8,
  LastWeight: { Lb: 95, Kg: 43.09 },
  LastSeries: 3,
  IsWaitingForSwap: false,
  RM: 105,
  RIR: 2,
  History: [],
  IsBodyweight: false,
  IsTimeBased: false,
  IsUnilateral: false,
  IsPlate: false,
  RecommendedCountdown: null,
  NegativeWeight: null,
  PartialWeight: null,
  IsNegative: false,
  IsPartial: false,
}

// Test wrapper
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: Infinity,
        networkMode: 'online', // This will respect navigator.onLine
        refetchOnMount: false,
        refetchOnWindowFocus: false,
        staleTime: 5 * 60 * 1000, // 5 minutes to match the hook configuration
      },
    },
  })

  return function ({ children }: { children: React.ReactNode }) {
    return (
      <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
    )
  }
}

describe('useWorkout - Optimistic Loading', () => {
  beforeEach(() => {
    // Reset all stores
    useAuthStore.setState({ isAuthenticated: true })
    useWorkoutStore.setState({
      cachedData: {
        userProgramInfo: null,
        userWorkouts: null,
        todaysWorkout: null,
        exerciseRecommendations: {},
        lastUpdated: {
          userProgramInfo: 0,
          userWorkouts: 0,
          todaysWorkout: 0,
          exerciseRecommendations: {},
        },
      },
      hasHydrated: false,
      currentWorkout: null,
      exercises: [],
      currentExerciseIndex: 0,
      currentSetIndex: 0,
      workoutSession: null,
      isLoading: false,
      error: null,
    })

    // Clear all mocks
    vi.clearAllMocks()

    // Mock navigator.onLine
    Object.defineProperty(navigator, 'onLine', {
      writable: true,
      value: true,
    })
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Cache-First Strategy', () => {
    it('should return cached data immediately when available', async () => {
      // Given: Stale cached data exists (triggers background refresh)
      const staleTimestamp = Date.now() - 25 * 60 * 60 * 1000 // 25 hours old
      useWorkoutStore.setState({
        hasHydrated: true,
        cachedData: {
          userProgramInfo: mockUserProgramInfo.GetUserProgramInfoResponseModel,
          userWorkouts: [mockWorkout],
          todaysWorkout: [mockWorkoutGroup],
          exerciseRecommendations: {},
          lastUpdated: {
            userProgramInfo: staleTimestamp,
            userWorkouts: staleTimestamp,
            todaysWorkout: staleTimestamp,
            exerciseRecommendations: {},
          },
        },
      })

      // Mock API to be slow
      vi.mocked(workoutApiService.getUserWorkoutProgramInfo).mockImplementation(
        () =>
          new Promise((resolve) => {
            setTimeout(() => resolve(mockUserProgramInfo), 1000)
          })
      )
      vi.mocked(workoutApiService.getUserWorkout).mockImplementation(
        () =>
          new Promise((resolve) => {
            setTimeout(() => resolve([mockWorkout]), 1000)
          })
      )
      vi.mocked(workoutApiService.getTodaysWorkout).mockImplementation(
        () =>
          new Promise((resolve) => {
            setTimeout(() => resolve([mockWorkoutGroup]), 1000)
          })
      )

      const { result } = renderHook(() => useWorkout(), {
        wrapper: createWrapper(),
      })

      // Then: Cached data should be available immediately
      expect(result.current.todaysWorkout).toBeDefined()
      expect(result.current.isLoadingWorkout).toBe(false) // Not loading because cache available
      expect(result.current.isLoadingFresh).toBe(true) // Loading fresh data in background

      // Wait for API calls to complete
      await waitFor(
        () => {
          expect(result.current.isLoadingFresh).toBe(false)
        },
        { timeout: 2000 }
      )
    })

    it('should show loading only when no cache available', async () => {
      // Given: No cached data, hydration complete
      useWorkoutStore.setState({
        hasHydrated: true,
        cachedData: {
          userProgramInfo: null,
          userWorkouts: null,
          todaysWorkout: null,
          exerciseRecommendations: {},
          lastUpdated: {
            userProgramInfo: 0,
            userWorkouts: 0,
            todaysWorkout: 0,
            exerciseRecommendations: {},
          },
        },
      })

      // Mock API responses
      vi.mocked(workoutApiService.getUserWorkoutProgramInfo).mockResolvedValue(
        mockUserProgramInfo
      )
      vi.mocked(workoutApiService.getUserWorkout).mockResolvedValue([
        mockWorkout,
      ])
      vi.mocked(workoutApiService.getTodaysWorkout).mockResolvedValue([
        mockWorkoutGroup,
      ])

      const { result } = renderHook(() => useWorkout(), {
        wrapper: createWrapper(),
      })

      // Then: Should show loading state
      expect(result.current.todaysWorkout).toBeNull()
      expect(result.current.isLoadingWorkout).toBe(true)

      // Wait for data to load
      await waitFor(() => {
        expect(result.current.isLoadingWorkout).toBe(false)
      })

      expect(result.current.todaysWorkout).toBeDefined()
    })

    it('should update cache after successful API fetch', async () => {
      // Given: Stale cached data
      const staleTimestamp = Date.now() - 25 * 60 * 60 * 1000 // 25 hours old
      const oldCachedData = {
        ...mockUserProgramInfo.GetUserProgramInfoResponseModel,
        ProgramLabel: 'Old Program',
      }

      useWorkoutStore.setState({
        hasHydrated: true,
        cachedData: {
          userProgramInfo: oldCachedData,
          userWorkouts: [mockWorkout],
          todaysWorkout: [mockWorkoutGroup],
          exerciseRecommendations: {},
          lastUpdated: {
            userProgramInfo: staleTimestamp,
            userWorkouts: staleTimestamp,
            todaysWorkout: staleTimestamp,
            exerciseRecommendations: {},
          },
        },
      })

      // Mock fresh API data
      const freshUserProgramInfo = {
        ...mockUserProgramInfo,
        GetUserProgramInfoResponseModel: {
          ...mockUserProgramInfo.GetUserProgramInfoResponseModel,
          ProgramLabel: 'Fresh Program',
        },
      }

      vi.mocked(workoutApiService.getUserWorkoutProgramInfo).mockResolvedValue(
        freshUserProgramInfo
      )
      vi.mocked(workoutApiService.getUserWorkout).mockResolvedValue([
        mockWorkout,
      ])
      vi.mocked(workoutApiService.getTodaysWorkout).mockResolvedValue([
        mockWorkoutGroup,
      ])

      const { result } = renderHook(() => useWorkout(), {
        wrapper: createWrapper(),
      })

      // Initially should have old cached data
      expect(result.current.todaysWorkout?.[0]?.Label).toBe('Beginner Program')

      // Wait for background refresh to complete
      await waitFor(
        () => {
          expect(result.current.isLoadingFresh).toBe(false)
        },
        { timeout: 2000 }
      )

      // Wait a bit more for cache to update
      await waitFor(
        () => {
          const state = useWorkoutStore.getState()
          expect(
            state.cachedData.userProgramInfo?.GetUserProgramInfoResponseModel
              ?.ProgramLabel
          ).toBe('Fresh Program')
        },
        { timeout: 3000 }
      )
    })

    it('should not fetch when cache is fresh', async () => {
      // Given: Create a new query client for this test
      const testQueryClient = new QueryClient({
        defaultOptions: {
          queries: {
            retry: false,
            gcTime: Infinity,
            staleTime: 5 * 60 * 1000, // 5 minutes
          },
        },
      })

      // Pre-populate the query cache with fresh data
      testQueryClient.setQueryData(['userProgramInfo'], mockUserProgramInfo, {
        updatedAt: Date.now() - 2 * 60 * 1000, // 2 minutes old
      })
      testQueryClient.setQueryData(['userWorkouts'], [mockWorkout], {
        updatedAt: Date.now() - 2 * 60 * 1000,
      })
      testQueryClient.setQueryData(['todaysWorkout'], [mockWorkoutGroup], {
        updatedAt: Date.now() - 2 * 60 * 1000,
      })

      // Fresh cached data (2 minutes old - within 5 minute stale time)
      const freshTimestamp = Date.now() - 2 * 60 * 1000

      useWorkoutStore.setState({
        hasHydrated: true,
        cachedData: {
          userProgramInfo: mockUserProgramInfo.GetUserProgramInfoResponseModel,
          userWorkouts: [mockWorkout],
          todaysWorkout: [mockWorkoutGroup],
          exerciseRecommendations: {},
          lastUpdated: {
            userProgramInfo: freshTimestamp,
            userWorkouts: freshTimestamp,
            todaysWorkout: freshTimestamp,
            exerciseRecommendations: {},
          },
        },
      })

      vi.mocked(workoutApiService.getUserWorkoutProgramInfo).mockResolvedValue(
        mockUserProgramInfo
      )
      vi.mocked(workoutApiService.getUserWorkout).mockResolvedValue([
        mockWorkout,
      ])
      vi.mocked(workoutApiService.getTodaysWorkout).mockResolvedValue([
        mockWorkoutGroup,
      ])

      const { result } = renderHook(() => useWorkout(), {
        wrapper: ({ children }) => (
          <QueryClientProvider client={testQueryClient}>
            {children}
          </QueryClientProvider>
        ),
      })

      // Should have cached data immediately
      expect(result.current.todaysWorkout).toBeDefined()
      expect(result.current.isLoadingWorkout).toBe(false)

      // Wait a tick to ensure React Query has processed
      await waitFor(() => {
        expect(result.current.isLoadingFresh).toBe(false)
      })

      // Should not make API calls since cache is fresh
      expect(workoutApiService.getUserWorkoutProgramInfo).not.toHaveBeenCalled()
      expect(workoutApiService.getUserWorkout).not.toHaveBeenCalled()
    })

    it('should handle stale cache gracefully', async () => {
      // Given: Stale cached data (25 hours old)
      const staleTimestamp = Date.now() - 25 * 60 * 60 * 1000

      useWorkoutStore.setState({
        hasHydrated: true,
        cachedData: {
          userProgramInfo: mockUserProgramInfo.GetUserProgramInfoResponseModel,
          userWorkouts: [mockWorkout],
          todaysWorkout: [mockWorkoutGroup],
          exerciseRecommendations: {},
          lastUpdated: {
            userProgramInfo: staleTimestamp,
            userWorkouts: staleTimestamp,
            todaysWorkout: staleTimestamp,
            exerciseRecommendations: {},
          },
        },
      })

      // Mock API responses
      vi.mocked(workoutApiService.getUserWorkoutProgramInfo).mockResolvedValue(
        mockUserProgramInfo
      )
      vi.mocked(workoutApiService.getUserWorkout).mockResolvedValue([
        mockWorkout,
      ])
      vi.mocked(workoutApiService.getTodaysWorkout).mockResolvedValue([
        mockWorkoutGroup,
      ])

      const { result } = renderHook(() => useWorkout(), {
        wrapper: createWrapper(),
      })

      // Should still show stale data immediately
      expect(result.current.todaysWorkout).toBeDefined()

      // Should trigger fresh fetch in background
      expect(workoutApiService.getUserWorkoutProgramInfo).toHaveBeenCalled()
      expect(workoutApiService.getUserWorkout).toHaveBeenCalled()
    })

    it('should cache exercise recommendations separately', async () => {
      // Given: Cached workout data and a recommendation
      useWorkoutStore.setState({
        hasHydrated: true,
        cachedData: {
          userProgramInfo: mockUserProgramInfo.GetUserProgramInfoResponseModel,
          userWorkouts: [mockWorkout],
          todaysWorkout: [mockWorkoutGroup],
          exerciseRecommendations: {
            1: mockRecommendation,
          },
          lastUpdated: {
            userProgramInfo: Date.now(),
            userWorkouts: Date.now(),
            todaysWorkout: Date.now(),
            exerciseRecommendations: {
              1: Date.now(),
            },
          },
        },
        currentWorkout: mockWorkout,
        exercises: mockWorkout.Exercises,
        currentExerciseIndex: 0,
      })

      vi.mocked(workoutApi.getUserProgramInfo).mockResolvedValue(
        mockUserProgramInfo
      )
      vi.mocked(workoutApi.getUserWorkout).mockResolvedValue([mockWorkout])
      vi.mocked(workoutApi.getExerciseRecommendation).mockResolvedValue(
        mockRecommendation
      )

      const { result } = renderHook(() => useWorkout(), {
        wrapper: createWrapper(),
      })

      // Get recommendation using the hook's method
      const recommendation = await result.current.getRecommendation(1)

      // Should have recommendation from cache
      expect(recommendation).toEqual(mockRecommendation)

      // Should not call API if cache is fresh
      expect(workoutApi.getExerciseRecommendation).not.toHaveBeenCalled()
    })
  })

  describe('Loading State Management', () => {
    it('should track granular loading states', async () => {
      // Given: No cache
      useWorkoutStore.setState({ hasHydrated: true })

      // Mock APIs with different delays
      vi.mocked(workoutApiService.getUserWorkoutProgramInfo).mockImplementation(
        () =>
          new Promise((resolve) => {
            setTimeout(() => resolve(mockUserProgramInfo), 100)
          })
      )
      vi.mocked(workoutApiService.getUserWorkout).mockImplementation(
        () =>
          new Promise((resolve) => {
            setTimeout(() => resolve([mockWorkout]), 200)
          })
      )
      vi.mocked(workoutApiService.getTodaysWorkout).mockImplementation(
        () =>
          new Promise((resolve) => {
            setTimeout(() => resolve([mockWorkoutGroup]), 200)
          })
      )

      const { result } = renderHook(() => useWorkout(), {
        wrapper: createWrapper(),
      })

      // Initially all loading
      // Note: loadingStates is a Map for exercise loading, not API loading
      // The correct check would be for isLoadingWorkout or individual query states
      expect(result.current.isLoadingWorkout).toBe(true)

      // Wait for all data to load
      await waitFor(() => {
        expect(result.current.isLoadingWorkout).toBe(false)
      })

      // Verify data was loaded
      expect(result.current.todaysWorkout).toBeDefined()
      expect(result.current.userProgramInfo).toBeDefined()
    })

    it('should indicate background refresh with isLoadingFresh', async () => {
      // Given: Stale cached data exists (to trigger background refresh)
      const staleTimestamp = Date.now() - 25 * 60 * 60 * 1000 // 25 hours old
      useWorkoutStore.setState({
        hasHydrated: true,
        cachedData: {
          userProgramInfo: mockUserProgramInfo.GetUserProgramInfoResponseModel,
          userWorkouts: [mockWorkout],
          todaysWorkout: [mockWorkoutGroup],
          exerciseRecommendations: {},
          lastUpdated: {
            userProgramInfo: staleTimestamp,
            userWorkouts: staleTimestamp,
            todaysWorkout: staleTimestamp,
            exerciseRecommendations: {},
          },
        },
      })

      // Mock slow API
      vi.mocked(workoutApi.getUserProgramInfo).mockImplementation(
        () =>
          new Promise((resolve) => {
            setTimeout(() => resolve(mockUserProgramInfo), 500)
          })
      )
      vi.mocked(workoutApi.getUserWorkout).mockImplementation(
        () =>
          new Promise((resolve) => {
            setTimeout(() => resolve([mockWorkout]), 500)
          })
      )

      const { result } = renderHook(() => useWorkout(), {
        wrapper: createWrapper(),
      })

      // Should have data immediately
      expect(result.current.todaysWorkout).toBeDefined()

      // Should not show loading state when cache available
      expect(result.current.isLoadingWorkout).toBe(false)

      // Should indicate background loading with isLoadingFresh
      expect(result.current.isLoadingFresh).toBe(true)

      // Wait for background refresh to complete
      await waitFor(
        () => {
          expect(result.current.isLoadingFresh).toBe(false)
        },
        { timeout: 1000 }
      )
    })
  })

  describe('Error Handling', () => {
    it('should fall back to cache on API error', async () => {
      // Given: Cached data exists
      useWorkoutStore.setState({
        hasHydrated: true,
        cachedData: {
          userProgramInfo: mockUserProgramInfo.GetUserProgramInfoResponseModel,
          userWorkouts: [mockWorkout],
          todaysWorkout: [mockWorkoutGroup],
          exerciseRecommendations: {},
          lastUpdated: {
            userProgramInfo: Date.now() - 25 * 60 * 60 * 1000, // 25 hours old (stale)
            userWorkouts: Date.now() - 25 * 60 * 60 * 1000,
            todaysWorkout: Date.now() - 25 * 60 * 60 * 1000,
            exerciseRecommendations: {},
          },
        },
      })

      // Mock API failure
      vi.mocked(workoutApiService.getUserWorkoutProgramInfo).mockRejectedValue(
        new Error('Network error')
      )
      vi.mocked(workoutApiService.getUserWorkout).mockRejectedValue(
        new Error('Network error')
      )
      vi.mocked(workoutApiService.getTodaysWorkout).mockRejectedValue(
        new Error('Network error')
      )

      const { result } = renderHook(() => useWorkout(), {
        wrapper: createWrapper(),
      })

      // Should still have cached data
      expect(result.current.todaysWorkout).toBeDefined()

      // Since we're using stale cache, the hook should fetch in background
      // Even with API error, cached data should still be available
      await waitFor(() => {
        expect(workoutApiService.getUserWorkoutProgramInfo).toHaveBeenCalled()
      })

      // Should still display cached data despite error
      expect(result.current.todaysWorkout).toBeDefined()

      // Error might not be set if we're using cache fallback
      // The important thing is that cached data is still available
    })

    it('should handle offline mode with cached data', async () => {
      // Given: Set offline mode first
      Object.defineProperty(navigator, 'onLine', {
        writable: true,
        value: false,
      })

      useWorkoutStore.setState({
        hasHydrated: true,
        cachedData: {
          userProgramInfo: mockUserProgramInfo.GetUserProgramInfoResponseModel,
          userWorkouts: [mockWorkout],
          todaysWorkout: [mockWorkoutGroup],
          exerciseRecommendations: {},
          lastUpdated: {
            userProgramInfo: Date.now(),
            userWorkouts: Date.now(),
            todaysWorkout: Date.now(),
            exerciseRecommendations: {},
          },
        },
      })

      // Mock API to fail (simulating offline)
      vi.mocked(workoutApiService.getUserWorkoutProgramInfo).mockRejectedValue(
        new Error('Network error')
      )
      vi.mocked(workoutApiService.getUserWorkout).mockRejectedValue(
        new Error('Network error')
      )
      vi.mocked(workoutApiService.getTodaysWorkout).mockRejectedValue(
        new Error('Network error')
      )

      const { result } = renderHook(() => useWorkout(), {
        wrapper: createWrapper(),
      })

      // Should have cached data even when offline
      expect(result.current.todaysWorkout).toBeDefined()
      expect(result.current.isOffline).toBe(true)
      expect(result.current.isLoadingWorkout).toBe(false) // Has cache, so not loading

      // Wait for any potential API attempts to complete
      await waitFor(() => {
        expect(result.current.isLoadingFresh).toBe(false)
      })

      // The key point is that cached data is still available despite being offline
      expect(result.current.todaysWorkout).toBeDefined()
      expect(result.current.userProgramInfo).toBeDefined()
    })
  })
})
