import { test, expect } from '@playwright/test'
import { login } from './helpers'

test.describe('Exercise Sets Display', () => {
  test.use({ viewport: { width: 375, height: 667 } }) // iPhone SE viewport

  test.beforeEach(async ({ page }) => {
    await page.goto('/login')
    await login(page, '<EMAIL>', 'Dr123456')
  })

  test('should display all sets with proper styling on mobile', async ({
    page,
  }) => {
    // Navigate to workout page
    await page.goto('/workout')
    await page.waitForLoadState('networkidle')

    // Click on the first exercise
    const firstExercise = page.locator('[data-testid="exercise-card"]').first()
    await firstExercise.click()

    // Wait for exercise page to load
    await page.waitForURL(/\/workout\/exercise\/\d+/)
    await page.waitForLoadState('networkidle')

    // Check that we're on the exercise page with sets displayed
    await expect(page.locator('text=Save Set')).toBeVisible()

    // Check for warmup sets section
    const warmupSection = page.locator('text=Warm-up Sets')
    const hasWarmups = await warmupSection.isVisible()

    if (hasWarmups) {
      // Verify warmup sets are displayed
      const warmupSets = page
        .locator('[data-testid*="set-"]')
        .filter({ hasText: 'Warm-up' })
      const warmupCount = await warmupSets.count()
      expect(warmupCount).toBeGreaterThan(0)

      // Check warmup set styling
      const warmupSetElements = page.locator(
        '.text-orange-600:has-text("Warm-up")'
      )
      await expect(warmupSetElements.first()).toBeVisible()
    }

    // Check for recommended sets display
    const recommendedSection = page.locator('text=/Recommended:.*×/')
    await expect(recommendedSection).toBeVisible()

    // Verify work sets are displayed (check for sets that have data-testid)
    const allSets = page.locator('[data-testid*="set-"]')
    const allSetCount = await allSets.count()
    expect(allSetCount).toBeGreaterThan(0)

    // Test set interaction
    const firstSet = allSets.first()
    await firstSet.click()

    // Check mobile layout responsiveness
    const box = await firstSet.boundingBox()
    expect(box?.width).toBeGreaterThan(250) // Should use most of mobile width

    // Take screenshot for visual verification
    await page.screenshot({
      path: 'tests/screenshots/exercise-sets-mobile.png',
      fullPage: true,
    })
  })

  test('should display rest-pause sets with additional info', async ({
    page,
  }) => {
    // Navigate to an exercise with rest-pause sets
    await page.goto('/workout/exercise/123') // Assuming this has rest-pause
    await page.waitForLoadState('networkidle')

    // Look for rest-pause indicator
    const restPauseIndicator = page.locator('text=Rest-pause').first()
    const hasRestPause = await restPauseIndicator.isVisible()

    if (hasRestPause) {
      // Check for pause info
      const pauseInfo = page.locator('text=/\\d+ pauses/')
      await expect(pauseInfo).toBeVisible()

      // Check for rest time info
      const restInfo = page.locator('text=/\\d+s rest/')
      await expect(restInfo).toBeVisible()
    }
  })

  test('should show historical sets when available', async ({ page }) => {
    await page.goto('/workout/exercise/123')
    await page.waitForLoadState('networkidle')

    // Check for historical sets section
    const historicalSection = page.locator('text=Previous Workout')
    const hasHistory = await historicalSection.isVisible()

    if (hasHistory) {
      // Verify historical sets are displayed
      const historicalSets = page.locator('text=/\\d+ × \\d+ lb/')
      const historyCount = await historicalSets.count()
      expect(historyCount).toBeGreaterThan(0)
    }
  })
})
