'use client'

import React from 'react'
import type { RecommendationModel } from '@/types'

interface ExplainerBoxProps {
  recommendation: RecommendationModel | null
  currentSetIndex: number
  isWarmup: boolean
  unit: 'kg' | 'lbs'
  currentReps?: number
  currentWeight?: number
  isFirstWorkSet?: boolean
}

export function ExplainerBox({
  recommendation,
  currentSetIndex,
  isWarmup,
  unit,
  currentReps,
  currentWeight,
  isFirstWorkSet = false,
}: ExplainerBoxProps) {
  if (!recommendation) return null

  const warmupCount = recommendation.WarmupsCount || 0
  const workSetCount = recommendation.Series || 0

  // Get the corresponding history set
  const historyIndex = isWarmup
    ? currentSetIndex
    : currentSetIndex - warmupCount
  const historySet = recommendation.HistorySet?.[historyIndex]

  // Calculate 1RM using Epley formula
  const calculate1RM = (weight: number, reps: number): number => {
    if (reps === 1) return weight
    return weight * (1 + reps / 30)
  }

  // Calculate 1RM progress for first work set
  let oneRMProgress = null
  if (
    isFirstWorkSet &&
    currentWeight &&
    currentReps &&
    recommendation.FirstWorkSet1RM
  ) {
    const current1RM = calculate1RM(currentWeight, currentReps)
    const previous1RM =
      unit === 'kg'
        ? recommendation.FirstWorkSet1RM.Kg
        : recommendation.FirstWorkSet1RM.Lb

    if (previous1RM && previous1RM > 0) {
      const percentageChange = ((current1RM - previous1RM) / previous1RM) * 100
      oneRMProgress = percentageChange
    }
  }

  return (
    <div className="px-4 py-3 bg-bg-secondary/50">
      <div className="text-center space-y-1">
        {/* Set structure info */}
        <p className="text-sm text-text-secondary italic">
          {warmupCount} warm-up{warmupCount !== 1 ? 's' : ''}, {workSetCount}{' '}
          work set{workSetCount !== 1 ? 's' : ''}
        </p>

        {/* Last time values */}
        {historySet && (
          <p className="text-sm text-text-secondary italic">
            Last time: {historySet.Reps} ×{' '}
            {unit === 'kg' ? historySet.Weight.Kg : historySet.Weight.Lb} {unit}
          </p>
        )}

        {/* 1RM Progress */}
        {oneRMProgress !== null && (
          <p className="text-sm font-medium text-brand-primary">
            1RM Progress: {oneRMProgress > 0 ? '+' : ''}
            {oneRMProgress.toFixed(2)}%
          </p>
        )}
      </div>
    </div>
  )
}
