import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import '@testing-library/jest-dom'
import { SetScreenWithGrid } from '../SetScreenWithGrid'
import { useSetScreenLogic } from '@/hooks/useSetScreenLogic'
import { NavigationProvider } from '@/contexts/NavigationContext'

// Mock hooks
vi.mock('@/hooks/useSetScreenLogic')
vi.mock('@/stores/authStore', () => ({
  useAuthStore: () => ({
    getCachedUserInfo: () => ({ MassUnit: 'lb' }),
  }),
}))

// Mock Next.js router
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    back: vi.fn(),
    refresh: vi.fn(),
  }),
  usePathname: () => '/workout/exercise/1',
}))

const mockUseSetScreenLogic = useSetScreenLogic as unknown as ReturnType<
  typeof vi.fn
>

describe('SetScreenWithGrid - Arrow Functionality', () => {
  const mockSetSetData = vi.fn()
  const mockHandleSaveSet = vi.fn()
  const mockRefetchRecommendation = vi.fn()

  const defaultMockLogic = {
    currentExercise: {
      Id: 1,
      Label: 'Bench Press',
      IsBodyweight: false,
    },
    recommendation: {
      Id: 1,
      Reps: 10,
      Weight: { Lb: 135, Kg: 61 },
      Series: 3,
      WarmupsCount: 0,
      WarmUpReps1: 5,
      WarmUpWeightSet1: { Lb: 95, Kg: 43 },
    },
    completedSets: [],
    isLoading: false,
    error: null,
    setData: { reps: 10, weight: 135 },
    setSetData: mockSetSetData,
    currentSetIndex: 0,
    showExerciseComplete: false,
    showComplete: false,
    isLastExercise: false,
    handleSaveSet: mockHandleSaveSet,
    refetchRecommendation: mockRefetchRecommendation,
    currentExerciseIndex: 0,
    exercises: [
      { Id: 1, Label: 'Bench Press', IsBodyweight: false },
      { Id: 2, Label: 'Squat', IsBodyweight: false },
    ],
  }

  beforeEach(() => {
    vi.clearAllMocks()
    mockUseSetScreenLogic.mockReturnValue(defaultMockLogic)
  })

  it('should update displayed reps when up arrow is clicked on active set', () => {
    render(
      <NavigationProvider>
        <SetScreenWithGrid />
      </NavigationProvider>
    )

    // Find the up arrow for reps on the active set
    const repsUpArrow = screen.getByLabelText('Increase reps')

    // The initial reps value should be displayed for the active set (set 1)
    const activeSetRepsInput = screen.getByLabelText('Reps for set 1')
    expect(activeSetRepsInput).toHaveValue(10)

    // Click the up arrow
    fireEvent.click(repsUpArrow)

    // Verify setSetData was called with updated value
    expect(mockSetSetData).toHaveBeenCalledWith(expect.any(Function))

    // Get the update function that was passed to setSetData
    const updateFn = mockSetSetData.mock.calls[0][0]
    const newData = updateFn({ reps: 10, weight: 135 })

    // Verify the update function returns correct new values
    expect(newData).toEqual({ reps: 11, weight: 135 })
  })

  it('should update displayed weight when down arrow is clicked on active set', () => {
    render(
      <NavigationProvider>
        <SetScreenWithGrid />
      </NavigationProvider>
    )

    // Find the down arrow for weight on the active set
    const weightDownArrow = screen.getByLabelText('Decrease weight')

    // The initial weight value should be displayed for the active set (set 1)
    const activeSetWeightInput = screen.getByLabelText('Weight for set 1')
    expect(activeSetWeightInput).toHaveValue(135)

    // Click the down arrow
    fireEvent.click(weightDownArrow)

    // Verify setSetData was called with updated value
    expect(mockSetSetData).toHaveBeenCalledWith(expect.any(Function))

    // Get the update function that was passed to setSetData
    const updateFn = mockSetSetData.mock.calls[0][0]
    const newData = updateFn({ reps: 10, weight: 135 })

    // Verify the update function returns correct new values (2.5 lbs decrement)
    expect(newData).toEqual({ reps: 10, weight: 132.5 })
  })

  it('should reflect setData changes in the displayed grid values', () => {
    const { rerender } = render(
      <NavigationProvider>
        <SetScreenWithGrid />
      </NavigationProvider>
    )

    // Initial values should be displayed for the active set (set 1)
    const activeSetRepsInput = screen.getByLabelText('Reps for set 1')
    const activeSetWeightInput = screen.getByLabelText('Weight for set 1')
    expect(activeSetRepsInput).toHaveValue(10)
    expect(activeSetWeightInput).toHaveValue(135)

    // Update the mock to return new setData values
    mockUseSetScreenLogic.mockReturnValue({
      ...defaultMockLogic,
      setData: { reps: 12, weight: 140 },
    })

    // Rerender to simulate state change
    rerender(
      <NavigationProvider>
        <SetScreenWithGrid />
      </NavigationProvider>
    )

    // Updated values should be displayed in the grid for the active set
    expect(screen.getByLabelText('Reps for set 1')).toHaveValue(12)
    expect(screen.getByLabelText('Weight for set 1')).toHaveValue(140)
  })
})
