import { Browser<PERSON>ontext, <PERSON> } from '@playwright/test'

export async function setupAuth(
  context: BrowserContext,
  email = '<EMAIL>'
) {
  await context.addInitScript((userEmail) => {
    const authState = {
      state: {
        user: {
          email: userEmail,
          FirstName: 'Test',
          LastName: 'User',
        },
        isAuthenticated: true,
        token: 'mock-jwt-token',
        refreshToken: 'mock-refresh-token',
        cachedUserInfo: null,
        cacheVersion: 1,
      },
      version: 0,
    }
    window.localStorage.setItem('drmuscle-auth', JSON.stringify(authState))
  }, email)
}

export async function mockWorkoutAPIs(page: Page) {
  // Mock program info
  await page.route('**/api/Workout/GetUserProgramInfo*', async (route) => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        GetUserProgramInfoResponseModel: {
          RecommendedProgram: {
            Id: 1,
            Label: 'Test Program',
            RemainingToLevelUp: 3,
          },
          NextWorkoutTemplate: {
            Id: 101,
            Label: 'Workout A',
            IsSystemExercise: false,
          },
        },
        TotalWorkoutCompleted: 10,
        ConsecutiveWeeks: 3,
      }),
    })
  })

  // Mock workout template groups
  await page.route(
    '**/api/Workout/GetUserWorkoutTemplateGroup*',
    async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            Id: 1,
            Label: 'Test Workout',
            WorkoutTemplates: [
              {
                Id: 101,
                Label: 'Day 1',
                Exercices: [
                  {
                    Id: 1,
                    Label: 'Bench Press',
                    IsNextExercise: true,
                    IsFinished: false,
                  },
                  {
                    Id: 2,
                    Label: 'Squat',
                    IsNextExercise: false,
                    IsFinished: false,
                  },
                ],
              },
            ],
          },
        ]),
      })
    }
  )

  // Mock exercise sets
  await page.route('**/api/Exercise/GetUserWorkoutSets*', async (route) => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify([]),
    })
  })

  // Mock recommendations
  await page.route(
    '**/api/Recommendation/GetRecommendation*',
    async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Reps: 10,
          Weight: { Lb: 100, Kg: 45.4 },
          RIR: 2,
        }),
      })
    }
  )

  // Mock user info
  await page.route('**/api/Account/GetUserInfo*', async (route) => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        Email: '<EMAIL>',
        FirstName: 'Test',
        LastName: 'User',
      }),
    })
  })

  // Mock login if needed
  await page.route('**/api/Account/Login*', async (route) => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        Token: 'mock-jwt-token',
        RefreshToken: 'mock-refresh-token',
        User: {
          Email: '<EMAIL>',
          FirstName: 'Test',
          LastName: 'User',
        },
      }),
    })
  })
}

export async function setupAuthenticatedUser(page: Page) {
  // Set up authentication state
  await page.addInitScript(() => {
    const authState = {
      state: {
        user: {
          email: '<EMAIL>',
          FirstName: 'Test',
          LastName: 'User',
        },
        isAuthenticated: true,
        token: 'mock-jwt-token',
        refreshToken: 'mock-refresh-token',
        cachedUserInfo: null,
        cacheVersion: 1,
      },
      version: 0,
    }
    window.localStorage.setItem('drmuscle-auth', JSON.stringify(authState))
  })

  // Mock APIs
  await mockWorkoutAPIs(page)
}

export async function login(
  page: Page,
  email = '<EMAIL>',
  password = 'Dr123456'
) {
  // Fill email field using role selector
  await page.getByRole('textbox', { name: 'Email' }).fill(email)

  // Fill password field using ID selector (password inputs don't have textbox role)
  await page.locator('#password').fill(password)

  // Wait for form validation to complete and button to be enabled
  await page.waitForFunction(
    () => {
      const button = document.querySelector(
        'button[type="submit"]'
      ) as HTMLButtonElement
      return button && !button.disabled
    },
    { timeout: 10000 }
  )

  // Use more specific selector and force click to bypass any potential overlay issues
  const loginButton = page.locator('button[type="submit"]')
  await loginButton.waitFor({ state: 'visible', timeout: 10000 })
  await loginButton.click({ force: true, timeout: 10000 })
}

export async function mockSuccessfulLogin(page: Page) {
  // Mock the login API
  await page.route('**/api/Account/Login*', async (route) => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        Token: 'mock-jwt-token',
        RefreshToken: 'mock-refresh-token',
        User: {
          Email: '<EMAIL>',
          FirstName: 'Test',
          LastName: 'User',
        },
      }),
    })
  })

  // Set up authentication state
  await setupAuthenticatedUser(page)
}
