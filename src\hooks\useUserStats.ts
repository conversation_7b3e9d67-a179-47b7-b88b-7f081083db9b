import { useEffect, useMemo, useCallback } from 'react'
import { useUserStatsStore } from '@/stores/userStatsStore'
import { logger } from '@/utils/logger'

/**
 * Hook to access user stats with automatic fetching
 */
export function useUserStats() {
  const stats = useUserStatsStore((state) => state.stats)
  const isLoading = useUserStatsStore((state) => state.isLoading)
  const error = useUserStatsStore((state) => state.error)
  const fetchStats = useUserStatsStore((state) => state.fetchStats)
  const isStale = useUserStatsStore((state) => state.isStale)
  const hasData = useUserStatsStore((state) => state.hasData)

  // Fetch stats on mount only if data is stale or missing
  useEffect(() => {
    // Only fetch if:
    // 1. We don't have data OR the data is stale
    // 2. Not currently loading
    // 3. No recent error
    const shouldFetch = (!hasData() || isStale()) && !isLoading && !error

    if (process.env.NODE_ENV === 'development') {
      logger.log('[useUserStats] Effect triggered', {
        hasData: hasData(),
        isStale: isStale(),
        isLoading,
        error,
        shouldFetch,
        stats,
      })
    }

    if (shouldFetch) {
      if (process.env.NODE_ENV === 'development') {
        logger.log('[useUserStats] Fetching stats...')
      }
      fetchStats()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hasData, isStale, isLoading, fetchStats]) // Intentionally omit error to prevent unwanted re-fetches

  // Memoize the default stats to prevent new object creation on every render
  const defaultStats = useMemo(
    () => ({ weekStreak: 0, workoutsCompleted: 0, lbsLifted: 0 }),
    []
  )

  const returnStats = stats || defaultStats

  // Memoize the refetch function to prevent new function creation on every render
  const refetch = useCallback(() => fetchStats(true), [fetchStats])

  if (process.env.NODE_ENV === 'development') {
    logger.log('[useUserStats] Returning stats', {
      returnStats,
      isLoading,
      error,
    })
  }

  return {
    stats: returnStats,
    isLoading,
    error,
    refetch,
  }
}

/**
 * Hook to prefetch user stats (used during login)
 */
export function usePrefetchUserStats() {
  const fetchStats = useUserStatsStore((state) => state.fetchStats)

  return {
    prefetch: () => {
      // Fire and forget - don't await
      fetchStats()
    },
  }
}
