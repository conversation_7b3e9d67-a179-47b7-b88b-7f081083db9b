'use client'

import React, { useEffect } from 'react'
import { useNavigation } from '@/contexts/NavigationContext'
import { useSetScreenLogic } from '@/hooks/useSetScreenLogic'
import { ExerciseSetsGrid } from './ExerciseSetsGrid'
import { ExerciseCompleteView } from './ExerciseCompleteView'
import { WorkoutCompleteView } from './WorkoutCompleteView'
import { SetScreenLoadingState } from './SetScreenLoadingState'
import { SetScreenErrorState } from './SetScreenErrorState'
import { RIRPicker } from './RIRPicker'
import { debugLog } from '@/utils/debugLog'
import type { WorkoutLogSerieModel } from '@/types'

interface SetScreenWithGridProps {
  exerciseId?: number
}

export function SetScreenWithGrid({ exerciseId }: SetScreenWithGridProps) {
  const { setTitle } = useNavigation()
  const {
    // State
    currentExercise,
    exercises,
    currentExerciseIndex,
    currentSetIndex,
    isSaving,
    saveError,
    showRIRPicker,
    showComplete,
    showExerciseComplete,
    isTransitioning,
    recommendation,
    isLoading,
    error,
    isLastExercise,
    completedSets,

    // Actions
    setSetData,
    handleSaveSet,
    handleRIRSelect,
    handleRIRCancel,
    refetchRecommendation,
  } = useSetScreenLogic(exerciseId)

  // Update navigation title with exercise name
  useEffect(() => {
    if (currentExercise) {
      setTitle(currentExercise.Label)
    }
  }, [currentExercise, setTitle])

  // Generate all sets based on recommendation and completed sets
  const generateAllSets = (): WorkoutLogSerieModel[] => {
    if (!recommendation) return []

    const sets: WorkoutLogSerieModel[] = []
    const warmupCount = recommendation.WarmupsCount || 0
    const workSetCount = recommendation.Series || 0

    // Add warmup sets
    for (let i = 0; i < warmupCount; i++) {
      const isCompleted = completedSets.some(
        (s) => s.IsWarmups && parseInt(s.SetNo || '0') === i + 1
      )
      const completedSet = completedSets.find(
        (s) => s.IsWarmups && parseInt(s.SetNo || '0') === i + 1
      )

      sets.push({
        Id: completedSet?.Id || -(i + 1), // Negative ID for uncompleted sets
        SetNo: `${i + 1}`,
        Reps: completedSet?.Reps || recommendation.WarmUpReps1 || 5,
        Weight: completedSet?.Weight || {
          Lb: recommendation.WarmUpWeightSet1?.Lb || 0,
          Kg: recommendation.WarmUpWeightSet1?.Kg || 0,
        },
        IsFinished: isCompleted,
        IsNext: !isCompleted && currentSetIndex === i,
        IsWarmups: true,
      })
    }

    // Add work sets
    for (let i = 0; i < workSetCount; i++) {
      const setIndex = warmupCount + i
      const isCompleted = completedSets.some(
        (s) => !s.IsWarmups && parseInt(s.SetNo || '0') === setIndex + 1
      )
      const completedSet = completedSets.find(
        (s) => !s.IsWarmups && parseInt(s.SetNo || '0') === setIndex + 1
      )

      sets.push({
        Id: completedSet?.Id || -(setIndex + 1),
        SetNo: `${setIndex + 1}`,
        Reps: completedSet?.Reps || recommendation.Reps || 10,
        Weight: completedSet?.Weight ||
          recommendation.Weight || { Lb: 0, Kg: 0 },
        IsFinished: isCompleted,
        IsNext: !isCompleted && currentSetIndex === setIndex,
        IsWarmups: false,
      })
    }

    // If no set is marked as next, mark the first incomplete set as next
    if (!sets.some((s) => s.IsNext)) {
      const firstIncompleteIndex = sets.findIndex((s) => !s.IsFinished)
      if (firstIncompleteIndex !== -1 && sets[firstIncompleteIndex]) {
        sets[firstIncompleteIndex].IsNext = true
      }
    }

    return sets
  }

  // Handle set update from grid
  const handleSetUpdate = (
    setId: number,
    updates: { reps?: number; weight?: number }
  ) => {
    debugLog('[SetScreenWithGrid] Updating set:', { setId, updates })

    // Find the set index
    const allSets = generateAllSets()
    const setIndex = allSets.findIndex(
      (s) => s.Id === setId || Math.abs(s.Id || 0) === setId
    )

    if (setIndex !== -1) {
      // Update the current set data
      setSetData((prev) => ({
        ...prev,
        reps: updates.reps ?? prev.reps,
        weight: updates.weight ?? prev.weight,
      }))
    }
  }

  // Handle finish exercise
  const handleFinishExercise = () => {
    debugLog('[SetScreenWithGrid] Finishing exercise')
    // Navigate to next exercise or complete workout
    if (isLastExercise) {
      // This would trigger workout complete
      handleSaveSet()
    } else {
      // Navigate to next exercise
      const nextExerciseIndex = (currentExerciseIndex ?? 0) + 1
      const nextExercise = exercises?.[nextExerciseIndex]
      if (nextExercise) {
        // Navigation would be handled by the parent/router
        debugLog(
          '[SetScreenWithGrid] Would navigate to next exercise:',
          nextExercise.Label
        )
      }
    }
  }

  // Handle add set
  const handleAddSet = () => {
    debugLog('[SetScreenWithGrid] Adding new set')
    // This would need to be implemented in the workout logic
    // For now, just log it
  }

  // Loading state
  if (isLoading && !recommendation) {
    return <SetScreenLoadingState />
  }

  // Error state
  if (error && !recommendation) {
    return <SetScreenErrorState onRetry={refetchRecommendation} />
  }

  // Exercise complete state
  if (showExerciseComplete) {
    return (
      <ExerciseCompleteView
        exerciseLabel={currentExercise?.Label}
        isLastExercise={isLastExercise}
        nextExerciseLabel={
          exercises && typeof currentExerciseIndex === 'number'
            ? exercises[currentExerciseIndex + 1]?.Label
            : undefined
        }
      />
    )
  }

  // Workout complete state
  if (showComplete) {
    return <WorkoutCompleteView />
  }

  const allSets = generateAllSets()

  return (
    <div className="min-h-[100dvh] bg-bg-primary flex flex-col">
      {/* Scrollable content area */}
      <div className="flex-1 overflow-y-auto pb-24">
        {/* Error Display */}
        {error && (
          <div className="bg-error/10 border-b border-error/20 px-4 py-3">
            <p className="text-error text-sm">{error.toString()}</p>
          </div>
        )}

        {/* Save error */}
        {saveError && (
          <div className="bg-error/10 border-b border-error/20 px-4 py-3">
            <p className="text-error text-sm">{saveError.toString()}</p>
            <button
              onClick={handleSaveSet}
              className="text-error underline text-sm mt-1"
            >
              Retry
            </button>
          </div>
        )}

        {/* Exercise Sets Grid */}
        {currentExercise && (
          <ExerciseSetsGrid
            exercise={currentExercise}
            recommendation={recommendation || undefined}
            sets={allSets}
            onSetUpdate={handleSetUpdate}
            onFinishExercise={handleFinishExercise}
            onAddSet={handleAddSet}
            onSaveCurrentSet={handleSaveSet}
            isSaving={isSaving || isTransitioning}
            unit="lbs" // This could come from user preferences
          />
        )}
      </div>

      {/* RIR Picker */}
      <RIRPicker
        isOpen={showRIRPicker}
        onSelect={handleRIRSelect}
        onCancel={handleRIRCancel}
      />
    </div>
  )
}
