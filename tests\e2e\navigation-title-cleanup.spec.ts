import { test, expect } from '@playwright/test'
import { setupAuthenticatedUser } from '../utils/auth-helper'

test.describe('Navigation Title Cleanup', () => {
  test.beforeEach(async ({ page }) => {
    await setupAuthenticatedUser(page)
  })

  test('should clear exercise name when navigating away from exercise page', async ({
    page,
  }) => {
    // Navigate to workout page
    await page.goto('/workout')
    await page.waitForLoadState('networkidle')

    // Check initial title
    const workoutTitle = await page.locator('header h1').textContent()
    expect(workoutTitle).toBe('Workout')

    // Navigate to an exercise (if available)
    const exerciseButton = page
      .locator('[role="button"]')
      .filter({ hasText: /Exercise:/ })
      .first()

    if (await exerciseButton.isVisible()) {
      await exerciseButton.click()
      await page.waitForLoadState('networkidle')

      // Wait for navigation to complete
      await page.waitForURL(/\/workout\/exercise\/\d+/)

      // The exercise name should be set by the exercise component
      // For now, just verify we're on the exercise page
      expect(page.url()).toMatch(/\/workout\/exercise\/\d+/)

      // Navigate back to workout page
      await page.goto('/workout')
      await page.waitForLoadState('networkidle')

      // Title should be back to "Workout", not the exercise name
      const titleAfterReturn = await page.locator('header h1').textContent()
      expect(titleAfterReturn).toBe('Workout')
    }
  })

  test('should show correct title when navigating to program page after exercise', async ({
    page,
  }) => {
    // Start at workout page
    await page.goto('/workout')

    // Navigate to program page
    await page.goto('/program')
    await page.waitForLoadState('networkidle')

    // Should show "Program" title
    const programTitle = await page.locator('header h1').textContent()
    expect(programTitle).toBe('Program')
  })
})
