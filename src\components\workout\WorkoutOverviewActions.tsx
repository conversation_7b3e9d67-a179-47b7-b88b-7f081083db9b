import { useState } from 'react'
import { useRouter } from 'next/navigation'
import type {
  WorkoutTemplateGroupModel,
  ExerciseWorkSetsModel,
  WorkoutSession,
  RecommendationModel,
  SetModel,
} from '@/types'
import { debugLog } from '@/utils/debugLog'

interface UseWorkoutActionsProps {
  todaysWorkout: WorkoutTemplateGroupModel[] | null
  startWorkout: (
    workout: WorkoutTemplateGroupModel[]
  ) => Promise<{ success: boolean; firstExerciseId?: number }>
  exercises: ExerciseWorkSetsModel[]
  workoutSession: WorkoutSession | null
  loadExerciseRecommendation: (
    exerciseId: number
  ) => Promise<RecommendationModel | null>
  updateExerciseWorkSets: (exerciseId: number, sets: SetModel[]) => void
  finishWorkout: () => Promise<boolean>
}

export function useWorkoutActions({
  todaysWorkout,
  startWorkout,
  exercises,
  workoutSession,
  loadExerciseRecommendation,
  updateExerciseWorkSets,
  finishWorkout,
}: UseWorkoutActionsProps) {
  const router = useRouter()
  const [isStartingWorkout, setIsStartingWorkout] = useState(false)

  const handleStartWorkout = async () => {
    if (todaysWorkout && !isStartingWorkout) {
      try {
        setIsStartingWorkout(true)
        // Start workout and wait for preload to complete
        const result = await startWorkout(todaysWorkout)

        if (result.success && result.firstExerciseId) {
          // Navigate using the first exercise ID from the workout that was just started
          router.push(`/workout/exercise/${result.firstExerciseId}`)
        }
      } catch (error) {
        debugLog.error('Failed to start workout:', error)
        // Handle error gracefully - could show a toast notification
      } finally {
        setIsStartingWorkout(false)
      }
    }
  }

  const handleFinishWorkout = async () => {
    try {
      await finishWorkout()
      router.push('/workout/complete')
    } catch (error) {
      // Handle error silently
    }
  }

  const handleExerciseClick = async (exerciseId: number) => {
    try {
      // Start workout if not already started
      if (todaysWorkout && !workoutSession) {
        const result = await startWorkout(todaysWorkout)
        if (!result.success) {
          debugLog.error('Failed to start workout')
          return
        }
      }

      // Pre-load the exercise recommendation before navigation
      const exercise = exercises?.find((ex) => ex.Id === exerciseId)
      if (exercise && (!exercise.sets || exercise.sets.length === 0)) {
        // Show loading state
        updateExerciseWorkSets(exerciseId, [])

        try {
          // Try to load recommendation before navigation
          await loadExerciseRecommendation(exerciseId)
        } catch (error) {
          // Continue with navigation even if recommendation fails
          // The exercise page will handle the retry
          // Only log in development
          debugLog.warn(
            'Failed to pre-load recommendation, continuing to exercise page:',
            error
          )
        }
      }

      // Navigate to exercise page
      router.push(`/workout/exercise/${exerciseId}`)
    } catch (error) {
      debugLog.error('Error handling exercise click:', error)
      // Show error toast or notification
      // For now, still navigate to let the exercise page handle the error
      router.push(`/workout/exercise/${exerciseId}`)
    }
  }

  const handleRetryExercise = (exerciseId: number) => {
    // Retry loading sets for a specific exercise
    updateExerciseWorkSets(exerciseId, [])
  }

  const hasCompletedSets =
    workoutSession?.exercises?.some(
      (exercise) => exercise.sets && exercise.sets.length > 0
    ) || false

  const getButtonLabel = () => {
    if (workoutSession && hasCompletedSets) return 'Finish and save workout'
    if (workoutSession) return 'Continue Workout'
    return 'Start Workout'
  }

  const getButtonAriaLabel = () => {
    if (workoutSession && hasCompletedSets) return 'Finish and save workout'
    if (workoutSession) return 'Continue your current workout'
    return 'Start a new workout session'
  }

  return {
    isStartingWorkout,
    handleStartWorkout,
    handleFinishWorkout,
    handleExerciseClick,
    handleRetryExercise,
    hasCompletedSets,
    getButtonLabel,
    getButtonAriaLabel,
  }
}
