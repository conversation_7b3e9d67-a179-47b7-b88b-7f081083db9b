import React, { useEffect, useState, useRef } from 'react'
import { StatCard, StatCardProps } from './StatCard'
import { logger } from '@/utils/logger'

interface LoadingAnimatedStatProps extends Omit<StatCardProps, 'value'> {
  /** The actual value from the API */
  value: number
  /** Whether the data is still loading */
  isLoading: boolean
  /** Estimated max value for loading animation */
  estimatedMax?: number
  /** Duration of loading animation in ms */
  loadingDuration?: number
}

export function LoadingAnimatedStat({
  value,
  isLoading,
  estimatedMax = 10,
  loadingDuration = 8000,
  ...statCardProps
}: LoadingAnimatedStatProps) {
  // Extract label to avoid dependency issues
  const { label } = statCardProps

  // Initialize with value if we already have it (prevents double animation on mount)
  const [displayValue, setDisplayValue] = useState(
    value > 0 && !isLoading ? value : 0
  )
  const [hasRealData, setHasRealData] = useState(value > 0 && !isLoading)
  const animationStartTime = useRef<number>(Date.now())
  const loadingAnimationRef = useRef<number | undefined>(undefined)
  const hasInitialized = useRef(false)

  // Debug logging
  if (process.env.NODE_ENV === 'development' && label) {
    logger.log(`[LoadingAnimatedStat] ${label}:`, {
      value,
      isLoading,
      displayValue,
      hasRealData,
      hasInitialized: hasInitialized.current,
    })
  }

  // Handle initial mount with real data
  useEffect(() => {
    if (!hasInitialized.current && value > 0 && !isLoading) {
      if (process.env.NODE_ENV === 'development' && label) {
        logger.log(
          `[LoadingAnimatedStat] ${label}: Initial mount with real data`,
          {
            value,
            isLoading,
          }
        )
      }
      hasInitialized.current = true
      setHasRealData(true)
      setDisplayValue(value)
    }
  }, [value, isLoading, label])

  useEffect(() => {
    // If we have real data, stop loading animation and show real value
    if (!isLoading && value > 0) {
      if (process.env.NODE_ENV === 'development' && label) {
        logger.log(
          `[LoadingAnimatedStat] ${label}: Real data received, stopping loading animation`,
          {
            value,
            isLoading,
            hasRealData,
          }
        )
      }
      if (loadingAnimationRef.current) {
        cancelAnimationFrame(loadingAnimationRef.current)
      }
      // Set hasRealData but don't update displayValue here
      // Let the finalValue calculation handle the transition
      setHasRealData(true)
      return
    }

    // Start loading animation only if still loading and we don't have real data yet
    if (isLoading && !hasRealData && value === 0) {
      if (process.env.NODE_ENV === 'development' && label) {
        logger.log(
          `[LoadingAnimatedStat] ${label}: Starting loading animation`,
          {
            isLoading,
            hasRealData,
            value,
            estimatedMax,
          }
        )
      }
      const animate = () => {
        const elapsed = Date.now() - animationStartTime.current
        const progress = Math.min(elapsed / loadingDuration, 1)

        // Use a very slow ease-in curve for loading animation (quartic)
        const easeIn = progress * progress * progress * progress
        const animatedValue = Math.round(estimatedMax * easeIn)

        setDisplayValue(animatedValue)

        // Continue animation if still loading
        if (progress < 1 && isLoading) {
          loadingAnimationRef.current = requestAnimationFrame(animate)
        }
      }

      loadingAnimationRef.current = requestAnimationFrame(animate)
    }

    return () => {
      if (loadingAnimationRef.current) {
        cancelAnimationFrame(loadingAnimationRef.current)
      }
    }
  }, [isLoading, value, hasRealData, estimatedMax, loadingDuration, label])

  // When we have real data, pass the real value directly
  // This prevents double animation by immediately showing the real value
  // instead of animating from the current loading value
  const finalValue =
    hasRealData || (!isLoading && value > 0) ? value : displayValue

  if (process.env.NODE_ENV === 'development' && label) {
    logger.log(`[LoadingAnimatedStat] ${label}: Final render`, {
      finalValue,
      hasRealData,
      isLoading,
      value,
      displayValue,
    })
  }

  return (
    <StatCard
      {...statCardProps}
      value={finalValue}
      isLoading={false} // Never show shimmer
      showShimmer={false}
    />
  )
}
