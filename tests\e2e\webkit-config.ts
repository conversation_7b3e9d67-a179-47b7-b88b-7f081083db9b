import { devices } from '@playwright/test'

/**
 * WebKit-specific configuration for CI stability
 */
export const webkitLaunchOptions = {
  timeout: 300000, // 5 minutes timeout for WebKit stability
  slowMo: 500, // Increased delay between actions for better stability
  headless: true,
  args: [
    // WebKit doesn't support Chrome/Chromium specific flags
    // Keep args array empty for WebKit
  ],
  // Environment variables for WebKit stability
  env: {
    ...process.env,
    WEBKIT_DISABLE_COMPOSITING: '1',
    WEBKIT_FORCE_COMPOSITING_MODE: '0',
  },
}

export const webkitContextOptions = {
  viewport: { width: 390, height: 844 },
  reducedMotion: 'reduce' as const,
  // Disable animations and features for stability
  forcedColors: 'none' as const,
  colorScheme: 'light' as const,
  // Disable service workers which can cause issues
  serviceWorkers: 'block' as const,
  // Set explicit locale
  locale: 'en-US',
  // Disable permissions that might cause prompts
  permissions: [] as string[],
}

export const webkitTestOptions = {
  // Increase timeouts for WebKit
  actionTimeout: 30000,
  navigationTimeout: 45000,
  // Add stability options
  screenshot: 'only-on-failure' as const,
  video: 'retain-on-failure' as const,
  trace: 'retain-on-failure' as const,
}

export const webkitProjectConfig = {
  name: 'Mobile Safari',
  use: {
    ...devices['iPhone 13'],
    viewport: { width: 390, height: 844 },
    hasTouch: true,
    isMobile: true,
    launchOptions: webkitLaunchOptions,
    contextOptions: webkitContextOptions,
    ...webkitTestOptions,
  },
  retries: 5, // Increased retries for WebKit instability
  workers: 1, // Single worker for WebKit to prevent resource exhaustion
}
